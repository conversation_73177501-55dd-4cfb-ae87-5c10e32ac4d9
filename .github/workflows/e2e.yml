name: E2E
on:
  push:
    branches:
      - main
      - develop
    paths:
      - '**.ts'
      - '**.tsx'
      - '**/tsconfig.json'
      - 'pnpm-lock.yaml'
      - '.github/workflows/e2e.yml'
  pull_request:
    paths:
      - '**.ts'
      - '**.tsx'
      - '**/tsconfig.json'
      - 'pnpm-lock.yaml'
      - '.github/workflows/e2e.yml'

defaults:
  run:
    shell: 'bash'

jobs:
  test_setup:
    name: Test setup
    runs-on: ubuntu-latest
    outputs:
      preview_url: ${{ steps.waitForVercelPreviewDeployment.outputs.url }}
    steps:
      - name: Wait for Vercel preview deployment to be ready
        uses: patrickedqvist/wait-for-vercel-preview@v1.3.1
        id: waitForVercelPreviewDeployment
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          max_timeout: 3000

  test_e2e:
    runs-on: ubuntu-latest
    needs: test_setup
    name: Playwright tests
    timeout-minutes: 15
    environment: ${{ vars.ENVIRONMENT_STAGE }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 14 # Need for npm >=7.7
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Install Playwright with deps
        run: npx playwright install --with-deps

      - name: Run tests
        run: npm run test:e2e
        env:
          PLAYWRIGHT_TEST_BASE_URL: ${{ needs.test_setup.outputs.preview_url }}

      - uses: actions/upload-artifact@v2
        if: always()
        with:
          name: playwright-report
          path: test-results/
          retention-days: 30