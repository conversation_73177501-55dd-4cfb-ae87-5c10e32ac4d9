# This workflow will build a docker container, publish it to Github Registry.
name: Build and Deploy Docker Container
on:
  release:
    types: [created]
  workflow_dispatch:

env:
  WEBAPP_IMAGE_NAME: bigcapitalhq/webapp
  SERVER_IMAGE_NAME: bigcapitalhq/server

jobs:
  build-publish-webapp:
    strategy:
      fail-fast: false
    name: Build and deploy webapp container
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to Container registry.
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
            images: ${{ env.WEBAPP_IMAGE_NAME }}

      # Builds and push the Docker image.
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        id: build
        with:
          context: ./
          file: ./packages/webapp/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          labels: ${{ steps.meta.outputs.labels }}
          tags: bigcapitalhq/webapp:latest, bigcapitalhq/webapp:${{github.ref_name}}

      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-webapp
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
      # Send notification to Slack channel.
      - name: Slack Notification built and published webapp container successfully.
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  build-publish-server:
    name: Build and deploy server container
    runs-on: ubuntu-latest
    steps:
      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to Container registry.
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # Builds and push the Docker image.
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        id: build
        with:
          context: ./
          file: ./packages/server/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: bigcapitalhq/server:latest, bigcapitalhq/server:${{github.ref_name}}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
  
      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-server
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

      # Send notification to Slack channel.
      - name: Slack Notification built and published server container successfully.
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
