
export const CustomersSampleData = [
  {
    "Customer Type": "Business",
    "First Name": "<PERSON><PERSON>",
    "Last Name": "<PERSON><PERSON><PERSON>",
    "Company Name": "<PERSON><PERSON> - Han<PERSON>",
    "Display Name": "<PERSON>",
    "Email": "<EMAIL>",
    "Personal Phone Number": "************",
    "Work Phone Number": "************",
    "Website": "http://google.com",
    "Opening Balance": 54302.23,
    "Opening Balance At": "2022-02-02",
    "Opening Balance Ex. Rate": 2,
    "Currency": "LYD",
    "Active": "F",
    "Note": "Doloribus autem optio temporibus dolores mollitia sit.",
    "Billing Address 1": "862 Jessika Well",
    "Billing Address 2": "1091 Dorthy Mount",
    "Billing Address City": "Deckowfort",
    "Billing Address Country": "Ghana",
    "Billing Address Phone": "************",
    "Billing Address Postcode": "38228",
    "Billing Address State": "Oregon",
    "Shipping Address 1": "37626 Thiel Villages",
    "Shipping Address 2": "132 Batz Avenue",
    "Shipping Address City": "Pagacburgh",
    "Shipping Address Country": "Albania",
    "Shipping Address Phone": "************",
    "Shipping Address Postcode": "13709",
    "Shipping Address State": "Georgia"
  },
  {
    "Customer Type": "Business",
    "First Name": "Hermann",
    "Last Name": "Crooks",
    "Company Name": "Veum - Schaefer",
    "Display Name": "Harley Veum",
    "Email": "<EMAIL>",
    "Personal Phone Number": "************",
    "Work Phone Number": "************",
    "Website": "http://google.com",
    "Opening Balance": 54302.23,
    "Opening Balance At": "2022-02-02",
    "Opening Balance Ex. Rate": 2,
    "Currency": "LYD",
    "Active": "T",
    "Note": "Doloribus dolore dolor dicta vitae in fugit nisi quibusdam.",
    "Billing Address 1": "532 Simonis Spring",
    "Billing Address 2": "3122 Nicolas Inlet",
    "Billing Address City": "East Matteofort",
    "Billing Address Country": "Holy See (Vatican City State)",
    "Billing Address Phone": "************",
    "Billing Address Postcode": "41607",
    "Billing Address State": "Montana",
    "Shipping Address 1": "2889 Tremblay Plaza",
    "Shipping Address 2": "71355 Kutch Isle",
    "Shipping Address City": "D'Amorehaven",
    "Shipping Address Country": "Monaco",
    "Shipping Address Phone": "************",
    "Shipping Address Postcode": "09634-0435",
    "Shipping Address State": "Nevada"
  },
  {
    "Customer Type": "Business",
    "First Name": "Nellie",
    "Last Name": "Gulgowski",
    "Company Name": "Boyle, Heller and Jones",
    "Display Name": "Randall Kohler",
    "Email": "<EMAIL>",
    "Personal Phone Number": "************",
    "Work Phone Number": "************",
    "Website": "http://google.com",
    "Opening Balance": 54302.23,
    "Opening Balance At": "2022-02-02",
    "Opening Balance Ex. Rate": 2,
    "Currency": "LYD",
    "Active": "T",
    "Note": "Vero quibusdam rem fugit aperiam est modi.",
    "Billing Address 1": "214 Sauer Villages",
    "Billing Address 2": "30687 Kacey Square",
    "Billing Address City": "Jayceborough",
    "Billing Address Country": "Benin",
    "Billing Address Phone": "************",
    "Billing Address Postcode": "16425-3887",
    "Billing Address State": "Mississippi",
    "Shipping Address 1": "562 Diamond Loaf",
    "Shipping Address 2": "9595 Satterfield Trafficway",
    "Shipping Address City": "Alexandrinefort",
    "Shipping Address Country": "Puerto Rico",
    "Shipping Address Phone": "************",
    "Shipping Address Postcode": "30258",
    "Shipping Address State": "South Dakota"
  },
  {
    "Customer Type": "Business",
    "First Name": "Stone",
    "Last Name": "Jerde",
    "Company Name": "Cassin, Casper and Maggio",
    "Display Name": "Clint McLaughlin",
    "Email": "<EMAIL>",
    "Personal Phone Number": "************",
    "Work Phone Number": "************",
    "Website": "http://google.com",
    "Opening Balance": 54302.23,
    "Opening Balance At": "2022-02-02",
    "Opening Balance Ex. Rate": 2,
    "Currency": "LYD",
    "Active": "F",
    "Note": "Quis cumque molestias rerum.",
    "Billing Address 1": "22590 Cathy Harbor",
    "Billing Address 2": "24493 Brycen Brooks",
    "Billing Address City": "Elnorashire",
    "Billing Address Country": "Andorra",
    "Billing Address Phone": "************",
    "Billing Address Postcode": "5680",
    "Billing Address State": "Nevada",
    "Shipping Address 1": "5355 Erdman Bridge",
    "Shipping Address 2": "421 Jeanette Camp",
    "Shipping Address City": "East Philip",
    "Shipping Address Country": "Venezuela",
    "Shipping Address Phone": "************",
    "Shipping Address Postcode": "34929-0501",
    "Shipping Address State": "Tennessee"
  },
  {
    "Customer Type": "Individual",
    "First Name": "Lempi",
    "Last Name": "Kling",
    "Company Name": "Schamberger, O'Connell and Bechtelar",
    "Display Name": "Alexie Barton",
    "Email": "<EMAIL>",
    "Personal Phone Number": "************",
    "Work Phone Number": "************",
    "Website": "http://google.com",
    "Opening Balance": 54302.23,
    "Opening Balance At": "2022-02-02",
    "Opening Balance Ex. Rate": 2,
    "Currency": "LYD",
    "Active": "F",
    "Note": "Maxime laboriosam hic voluptate maiores est officia.",
    "Billing Address 1": "0851 Jones Flat",
    "Billing Address 2": "845 Bailee Drives",
    "Billing Address City": "Kamrenport",
    "Billing Address Country": "Niger",
    "Billing Address Phone": "************",
    "Billing Address Postcode": "30311",
    "Billing Address State": "Delaware",
    "Shipping Address 1": "929 Ferry Row",
    "Shipping Address 2": "020 Adam Plaza",
    "Shipping Address City": "West Carmellaside",
    "Shipping Address Country": "Ghana",
    "Shipping Address Phone": "************",
    "Shipping Address Postcode": "79221-4681",
    "Shipping Address State": "Illinois"
  }
]
