/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table('payment_receives', (table) => {
    table.string('stripe_pintent_id').nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table('payment_receives', (table) => {
    table.dropColumn('stripe_pintent_id');
  });
};
